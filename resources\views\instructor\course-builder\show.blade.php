@extends('layouts.app')

@section('title', 'Course Builder - ' . $course->title)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/instructor/course-builder-show.css') }}">
@endpush

@section('content')
<div class="min-h-screen bg-black" data-course-id="{{ $course->slug }}">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-full px-4 sm:px-6 lg:px-8">
            <div class="py-4">
                <!-- Mobile/Tablet/Desktop Header Layout -->
                <div class="flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <!-- Mobile/Tablet: Separate rows for better space management -->
                    <div class="lg:hidden">
                        <!-- Mobile/Tablet Top Row: Back button and Menu Toggle -->
                        <div class="flex items-center justify-between mb-2">
                            <a href="{{ route('instructor.courses.index') }}"
                               class="text-gray-400 hover:text-white transition-colors text-sm flex items-center">
                                <i class="fas fa-arrow-left mr-1"></i>
                                <span>Back</span>
                            </a>
                            <button id="mobile-sidebar-toggle"
                                    class="text-gray-400 hover:text-white transition-colors p-2">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        <!-- Mobile/Tablet Title Row: Full width for title -->
                        <div class="w-full">
                            <h1 class="text-lg md:text-xl font-bold text-white truncate pr-4">{{ $course->title }}</h1>
                        </div>
                    </div>

                    <!-- Desktop Layout: Traditional horizontal layout -->
                    <div class="hidden lg:flex lg:items-center lg:space-x-4">
                        <a href="{{ route('instructor.courses.index') }}"
                           class="text-gray-400 hover:text-white transition-colors text-base">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>Back to Courses</span>
                        </a>
                        <div class="h-6 w-px bg-gray-700"></div>
                        <h1 class="text-xl font-bold text-white">{{ $course->title }}</h1>
                    </div>

                    <!-- Action Buttons Row: Save Status and Publish Button -->
                    <div class="flex items-center justify-end space-x-2 md:space-x-3">
                        <!-- Save status indicator -->
                        <div id="save-status-indicator" class="hidden px-2 py-1 md:px-3 md:py-1 rounded-lg text-xs md:text-sm font-medium">
                            <i class="fas fa-check-circle mr-1 md:mr-2"></i>
                            <span id="save-status-text" class="hidden sm:inline">Changes saved</span>
                            <span class="sm:hidden">Saved</span>
                        </div>

                        <!-- Featured toggle button -->
                        <button id="featured-toggle-btn"
                                class="px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors {{ $course->featured ? 'bg-yellow-600 hover:bg-yellow-700 text-white' : 'bg-gray-600 hover:bg-gray-700 text-white' }}"
                                data-course-id="{{ $course->slug }}"
                                data-current-featured="{{ $course->featured ? 'true' : 'false' }}">
                            <i class="fas {{ $course->featured ? 'fa-star' : 'fa-star' }} mr-1 md:mr-2"></i>
                            <span class="hidden sm:inline">{{ $course->featured ? 'Featured' : 'Feature' }}</span>
                            <span class="sm:hidden">{{ $course->featured ? 'Featured' : 'Feature' }}</span>
                        </button>

                        <!-- Publish/Unpublish button -->
                        <button id="publish-toggle-btn"
                                class="px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors {{ $course->status === 'published' ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white' }}"
                                data-course-id="{{ $course->slug }}"
                                data-current-status="{{ $course->status }}">
                            <i class="fas {{ $course->status === 'published' ? 'fa-eye' : 'fa-eye-slash' }} mr-1 md:mr-2"></i>
                            <span class="hidden sm:inline">{{ $course->status === 'published' ? 'Published' : 'Publish Course' }}</span>
                            <span class="sm:hidden">{{ $course->status === 'published' ? 'Live' : 'Publish' }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col lg:flex-row min-h-screen">
        <!-- Mobile/Tablet Sidebar Overlay -->
        <div id="mobile-sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

        <!-- Sidebar - Curriculum Panel -->
        <div id="curriculum-sidebar"
             class="fixed inset-y-0 left-0 z-50 w-80 bg-gray-900 border-r border-gray-800 overflow-y-auto transform -translate-x-full transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:w-1/3 lg:z-auto">
            <div class="p-4 lg:p-6">
                <!-- Mobile/Tablet Sidebar Header -->
                <div class="flex items-center justify-between mb-4 lg:hidden">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="mobile-sidebar-close" class="text-gray-400 hover:text-white transition-colors p-2">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Desktop Sidebar Header -->
                <div class="hidden lg:flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="add-chapter-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Mobile/Tablet Add Chapter Button -->
                <div class="mb-4 lg:hidden">
                    <button id="add-chapter-btn-mobile"
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Curriculum Tree -->
                <div id="curriculum-tree" class="space-y-2">
                    @forelse($course->chapters as $chapter)
                        <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700" 
                             data-chapter-id="{{ $chapter->id }}"
                             data-chapter-index="{{ $loop->index }}">
                            <!-- Chapter Header -->
                            <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                                 onclick="selectItem('chapter', '{{ $chapter->id }}')">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                            <i class="fas fa-grip-vertical"></i>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-folder text-yellow-500"></i>
                                            <span class="text-white font-medium">{{ $chapter->title }}</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-gray-400">{{ $chapter->lectures->count() }} lectures</span>
                                        <div class="flex items-center space-x-1">
                                            @if($chapter->is_published)
                                                <i class="fas fa-eye text-green-500 text-xs published-icon" title="Published"></i>
                                            @else
                                                <i class="fas fa-eye-slash text-gray-500 text-xs published-icon" title="Unpublished"></i>
                                            @endif

                                        </div>
                                        <button class="text-gray-400 hover:text-white transition-colors"
                                                onclick="event.stopPropagation(); toggleChapter('{{ $chapter->id }}')">
                                            <i class="fas fa-chevron-down chapter-toggle"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Chapter Lectures -->
                            <div class="chapter-lectures pl-8 pb-2" id="chapter-lectures-{{ $chapter->id }}">
                                @foreach($chapter->lectures as $lecture)
                                    <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
                                         data-lecture-id="{{ $lecture->id }}"
                                         data-lecture-index="{{ $loop->index }}"
                                         onclick="selectItem('lecture', '{{ $lecture->id }}')">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                                    <i class="fas fa-grip-vertical text-xs"></i>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    @switch($lecture->type)
                                                        @case('video')
                                                            <i class="fas fa-play-circle text-blue-500"></i>
                                                            @break
                                                        @case('text')
                                                            <i class="fas fa-file-text text-green-500"></i>
                                                            @break
                                                        @case('quiz')
                                                            <i class="fas fa-question-circle text-purple-500"></i>
                                                            @break
                                                        @case('assignment')
                                                            <i class="fas fa-tasks text-orange-500"></i>
                                                            @break
                                                        @case('resource')
                                                            <i class="fas fa-download text-gray-500"></i>
                                                            @break
                                                        @default
                                                            <i class="fas fa-file text-gray-500"></i>
                                                    @endswitch
                                                    <span class="text-gray-300 text-sm">{{ $lecture->title }}</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                @if($lecture->duration_minutes)
                                                    <span class="text-xs text-gray-500">{{ $lecture->duration_minutes }}min</span>
                                                @endif
                                                <div class="flex items-center space-x-1">
                                                    @if($lecture->is_published)
                                                        <i class="fas fa-eye text-green-500 text-xs published-icon" title="Published"></i>
                                                    @else
                                                        <i class="fas fa-eye-slash text-gray-500 text-xs published-icon" title="Unpublished"></i>
                                                    @endif
                                                    @if($lecture->is_free_preview)
                                                        <i class="fas fa-unlock text-blue-500 text-xs preview-icon" title="Free Preview"></i>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Add Lecture Button -->
                                <div class="p-3">
                                    <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                                            data-chapter-id="{{ $chapter->id }}"
                                            onclick="addLecture('{{ $chapter->id }}')">
                                        <i class="fas fa-plus mr-2"></i>Add Lecture
                                    </button>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div id="empty-curriculum" class="text-center py-12">
                            <div class="text-gray-500 mb-4">
                                <i class="fas fa-folder-open text-4xl"></i>
                            </div>
                            <p class="text-gray-400 mb-4">No chapters yet</p>
                            <button id="add-first-chapter-btn" 
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Your First Chapter
                            </button>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Main Editor Panel -->
        <div class="flex-1 bg-black overflow-y-auto lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Course Overview Section -->
                <div id="course-overview" class="mb-6 md:mb-8">
                    <div class="bg-gradient-to-r from-gray-900 to-gray-800 border border-gray-700 rounded-lg p-4 md:p-6">
                        <!-- Mobile Layout -->
                        <div class="block md:hidden">
                            <div class="mb-4">
                                <h2 class="text-xl font-bold text-white mb-2">{{ $course->title }}</h2>
                                @if($course->subtitle)
                                    <p class="text-gray-300 mb-3 text-sm">{{ $course->subtitle }}</p>
                                @endif
                            </div>

                            <!-- Mobile Stats Grid -->
                            <div class="grid grid-cols-2 gap-3 mb-4 text-xs text-gray-400">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-tag"></i>
                                    <span class="truncate">{{ $course->category->name ?? 'No Category' }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-signal"></i>
                                    <span class="capitalize">{{ str_replace('_', ' ', $course->level) }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-book"></i>
                                    <span>{{ $course->chapters->count() }} Chapters</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-play-circle"></i>
                                    <span>{{ $course->lectures->count() }} Lectures</span>
                                </div>
                            </div>

                            <!-- Mobile Price and Edit Button -->
                            <div class="flex items-center justify-between">
                                <div>
                                    @if($course->price > 0)
                                        <span class="text-xl font-bold text-green-400">${{ number_format($course->price, 2) }}</span>
                                        <div class="text-xs text-gray-400">Paid Course</div>
                                    @else
                                        <span class="text-xl font-bold text-blue-400">FREE</span>
                                        <div class="text-xs text-gray-400">Free Course</div>
                                    @endif
                                </div>
                                <button onclick="selectItem('course', '{{ $course->slug }}')"
                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                            </div>
                        </div>

                        <!-- Desktop Layout -->
                        <div class="hidden md:block">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h2 class="text-2xl font-bold text-white mb-2">{{ $course->title }}</h2>
                                    @if($course->subtitle)
                                        <p class="text-gray-300 mb-3">{{ $course->subtitle }}</p>
                                    @endif
                                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-tag"></i>
                                            <span>{{ $course->category->name ?? 'No Category' }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-signal"></i>
                                            <span class="capitalize">{{ str_replace('_', ' ', $course->level) }}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-book"></i>
                                            <span>{{ $course->chapters->count() }} Chapters</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-play-circle"></i>
                                            <span>{{ $course->lectures->count() }} Lectures</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="mb-2">
                                        @if($course->price > 0)
                                            <span class="text-2xl font-bold text-green-400">${{ number_format($course->price, 2) }}</span>
                                            <div class="text-xs text-gray-400">Paid Course</div>
                                        @else
                                            <span class="text-2xl font-bold text-blue-400">FREE</span>
                                            <div class="text-xs text-gray-400">Free Course</div>
                                        @endif
                                    </div>
                                    <button onclick="selectItem('course', '{{ $course->slug }}')"
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-edit mr-2"></i>Edit Details
                                    </button>
                                </div>
                            </div>
                        </div>

                        @if($course->description)
                            <div class="border-t border-gray-700 pt-4">
                                <p class="text-gray-300 text-sm leading-relaxed">{{ Str::limit($course->description, 200) }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Welcome State -->
                <div id="welcome-state" class="{{ $course->chapters->count() > 0 ? 'hidden' : '' }}">
                    <div class="text-center py-16">
                        <div class="text-gray-600 mb-6">
                            <i class="fas fa-edit text-6xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">Welcome to Course Builder</h3>
                        <p class="text-gray-400 mb-8 max-w-md mx-auto">
                            Create engaging course content with our intuitive builder.
                            Start by adding your first chapter from the sidebar.
                        </p>
                        <div class="space-y-4">
                            <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-left max-w-md mx-auto">
                                <h4 class="text-white font-medium mb-2">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    Pro Tips
                                </h4>
                                <ul class="text-gray-400 text-sm space-y-1">
                                    <li>• Organize content into logical chapters</li>
                                    <li>• Mix different content types for engagement</li>
                                    <li>• Use auto-save - no manual saving needed!</li>
                                    <li>• Drag and drop to reorder content</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Details Editor -->
                <div id="course-editor" class="hidden">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 md:p-6">
                        <h3 class="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Course Details</h3>

                        <form id="course-details-form" class="space-y-4 md:space-y-6">
                            @csrf
                            <!-- Mobile: Stack all fields, Desktop: Two columns for title/subtitle -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Title <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="title" value="{{ $course->title }}"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course title">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Subtitle
                                    </label>
                                    <input type="text" name="subtitle" value="{{ $course->subtitle }}"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course subtitle">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" rows="4"
                                          class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                          placeholder="Describe what students will learn in this course">{{ $course->description }}</textarea>
                            </div>

                            <!-- What You'll Learn -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    What You'll Learn
                                </label>
                                <textarea name="what_youll_learn_text" rows="6"
                                          class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                          placeholder="Describe the key learning outcomes and skills students will gain from this course. Use **bold text** for emphasis.">{{ $course->what_youll_learn_text }}</textarea>
                                <p class="text-xs text-gray-400 mt-1">
                                    This content will be displayed prominently on your course page. Use **text** for bold formatting.
                                </p>
                            </div>

                            <!-- Course Preview Section -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Preview
                                </label>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-400 mb-2">
                                            Preview Type
                                        </label>
                                        <select name="preview_type" id="preview-type-select"
                                                class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                                onchange="togglePreviewContent()">
                                            <option value="image" {{ $course->preview_type == 'image' ? 'selected' : '' }}>Image</option>
                                            <option value="video" {{ $course->preview_type == 'video' ? 'selected' : '' }}>Video</option>
                                            <option value="youtube" {{ $course->preview_type == 'youtube' ? 'selected' : '' }}>YouTube Link</option>
                                        </select>
                                    </div>
                                    
                                    <div id="preview-content-field">
                                        <label class="block text-xs font-medium text-gray-400 mb-2" id="preview-content-label">
                                            Preview Content
                                        </label>
                                        
                                        <!-- Text input for video and youtube -->
                                        <input type="text" name="preview_content" id="preview-content-input" value="{{ $course->preview_content }}"
                                               class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                               placeholder="Enter preview content">
                                        
                                        <!-- File upload for image -->
                                        <div id="preview-image-upload" class="hidden">
                                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md bg-gray-900">
                                                <div class="space-y-1 text-center">
                                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>
                                                    <div class="flex text-sm text-gray-400">
                                                        <label for="preview-image-file" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-red-500 hover:text-red-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500">
                                                            <span>Upload an image</span>
                                                            <input id="preview-image-file" name="preview_image" type="file" class="sr-only" accept="image/*">
                                                        </label>
                                                        <p class="pl-1">or drag and drop</p>
                                                    </div>
                                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                                </div>
                                            </div>
                                            
                                            <!-- Image preview -->
                                            <div id="preview-image-display" class="mt-4 hidden">
                                                <img id="preview-image-preview" src="" alt="Preview" class="w-full max-w-md mx-auto rounded-lg border border-gray-600">
                                                <div class="mt-2 text-center">
                                                    <button type="button" onclick="removePreviewImage()" class="text-red-500 hover:text-red-400 text-sm">
                                                        <i class="fas fa-trash mr-1"></i>Remove Image
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <p class="text-xs text-gray-400 mt-1" id="preview-content-help">
                                            Enter the URL or path for your course preview
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Thumbnail Section -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Thumbnail
                                </label>
                                <div class="space-y-4">
                                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md bg-gray-900">
                                        <div class="space-y-1 text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="flex text-sm text-gray-400">
                                                <label for="course-thumbnail-file-mobile" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-red-500 hover:text-red-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500">
                                                    <span>Upload course thumbnail</span>
                                                    <input id="course-thumbnail-file-mobile" name="course_image" type="file" class="sr-only" accept="image/*">
                                                </label>
                                                <p class="pl-1">or drag and drop</p>
                                            </div>
                                            <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Thumbnail preview -->
                                    <div id="course-thumbnail-display-mobile" class="{{ $course->image ? '' : 'hidden' }}">
                                        <img id="course-thumbnail-preview-mobile" src="{{ $course->image ? $course->getImageUrl() : '' }}" alt="Course Thumbnail" class="w-full max-w-md mx-auto rounded-lg border border-gray-600">
                                        <div class="mt-2 text-center">
                                            <button type="button" onclick="removeCourseThumbnailMobile()" class="text-red-500 hover:text-red-400 text-sm">
                                                <i class="fas fa-trash mr-1"></i>Remove Thumbnail
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <p class="text-xs text-gray-400">This image will be displayed on course listings and as the main course image. Recommended size: 400x225 pixels.</p>
                                </div>
                            </div>

                            <!-- Mobile: Stack all fields, Desktop: Three columns -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <select name="category_id" id="category-select"
                                                class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base pr-12">
                                            <option value="">Select Category</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}" {{ $course->category_id == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <button type="button" id="add-category-btn"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-red-500 hover:text-red-400 transition-colors p-1 rounded"
                                                title="Add New Category">
                                            <i class="fas fa-plus text-sm"></i>
                                        </button>
                                    </div>

                                    <!-- Add Category Modal/Form (Initially Hidden) -->
                                    <div id="add-category-form" class="hidden mt-3 p-4 bg-gray-900 border border-gray-700 rounded-lg">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="text-sm font-medium text-gray-300">Add New Category</h4>
                                            <button type="button" id="cancel-add-category" class="text-gray-500 hover:text-gray-400">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="space-y-3">
                                            <div>
                                                <input type="text" id="new-category-name" placeholder="Category Name"
                                                       class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded text-white text-sm focus:border-red-500 focus:ring-1 focus:ring-red-500">
                                            </div>
                                            <div>
                                                <input type="text" id="new-category-description" placeholder="Description (optional)"
                                                       class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded text-white text-sm focus:border-red-500 focus:ring-1 focus:ring-red-500">
                                            </div>
                                            <div class="flex space-x-2">
                                                <button type="button" id="save-new-category"
                                                        class="flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                                                    <i class="fas fa-save mr-1"></i> Save
                                                </button>
                                                <button type="button" id="cancel-new-category"
                                                        class="flex-1 px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors">
                                                    Cancel
                                                </button>
                                            </div>
                                        </div>
                                        <div id="category-form-feedback" class="mt-2 text-sm hidden"></div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Level <span class="text-red-500">*</span>
                                    </label>
                                    <select name="level"
                                            class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base">
                                        <option value="beginner" {{ $course->level == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="intermediate" {{ $course->level == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="advanced" {{ $course->level == 'advanced' ? 'selected' : '' }}>Advanced</option>
                                        <option value="all_levels" {{ $course->level == 'all_levels' ? 'selected' : '' }}>All Levels</option>
                                    </select>
                                </div>

                                <div class="md:col-span-2 lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                                        <input type="number" name="price" value="{{ $course->price }}" step="0.01" min="0" max="999.99"
                                               class="w-full pl-8 pr-3 py-3 md:pr-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                               placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                                <button type="button" onclick="saveCourse()"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                                    <i class="fas fa-save mr-2"></i>Save Course Details
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Welcome State -->
                <div id="welcome-state" class="{{ $course->chapters->count() > 0 ? 'hidden' : '' }}">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-8 text-center">
                        <div class="text-gray-500 mb-4">
                            <i class="fas fa-graduation-cap text-4xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">Welcome to Course Builder</h3>
                        <p class="text-gray-400 mb-6">Start building your course by adding your first chapter. Each chapter can contain multiple lectures with different content types.</p>
                        <button onclick="addChapter()"
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            <i class="fas fa-plus mr-2"></i>Add Your First Chapter
                        </button>
                    </div>
                </div>

                <!-- Course Editor -->
                <div id="course-editor" class="hidden">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-bold text-white">Course Details</h3>
                        </div>

                        <form id="course-details-form" class="space-y-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Title <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="title" value="{{ $course->title }}"
                                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                           placeholder="Enter course title">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Subtitle
                                    </label>
                                    <input type="text" name="subtitle" value="{{ $course->subtitle }}"
                                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                           placeholder="Brief subtitle for your course">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" rows="4"
                                          class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                          placeholder="Describe what students will learn in this course">{{ $course->description }}</textarea>
                            </div>

                            <!-- What You'll Learn -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    What You'll Learn
                                </label>
                                <textarea name="what_youll_learn_text" rows="6"
                                          class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                          placeholder="Describe the key learning outcomes and skills students will gain from this course. Use **bold text** for emphasis.">{{ $course->what_youll_learn_text }}</textarea>
                                <p class="text-xs text-gray-400 mt-1">
                                    This content will be displayed prominently on your course page. Use **text** for bold formatting.
                                </p>
                            </div>

                            <!-- Course Preview Section -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Preview
                                </label>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-400 mb-2">
                                            Preview Type
                                        </label>
                                        <select name="preview_type" id="preview-type-select-desktop"
                                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                                onchange="togglePreviewContent()">
                                            <option value="image" {{ $course->preview_type == 'image' ? 'selected' : '' }}>Image</option>
                                            <option value="video" {{ $course->preview_type == 'video' ? 'selected' : '' }}>Video</option>
                                            <option value="youtube" {{ $course->preview_type == 'youtube' ? 'selected' : '' }}>YouTube Link</option>
                                        </select>
                                    </div>
                                    
                                    <div id="preview-content-field-desktop">
                                        <label class="block text-xs font-medium text-gray-400 mb-2" id="preview-content-label-desktop">
                                            Preview Content
                                        </label>
                                        
                                        <!-- Text input for video and youtube -->
                                        <input type="text" name="preview_content" id="preview-content-input-desktop" value="{{ $course->preview_content }}"
                                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                               placeholder="Enter preview content">
                                        
                                        <!-- File upload for image -->
                                        <div id="preview-image-upload-desktop" class="hidden">
                                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md bg-gray-900">
                                                <div class="space-y-1 text-center">
                                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>
                                                    <div class="flex text-sm text-gray-400">
                                                        <label for="preview-image-file-desktop" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-red-500 hover:text-red-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500">
                                                            <span>Upload an image</span>
                                                            <input id="preview-image-file-desktop" name="preview_image" type="file" class="sr-only" accept="image/*">
                                                        </label>
                                                        <p class="pl-1">or drag and drop</p>
                                                    </div>
                                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                                </div>
                                            </div>
                                            
                                            <!-- Image preview -->
                                            <div id="preview-image-display-desktop" class="mt-4 hidden">
                                                <img id="preview-image-preview-desktop" src="" alt="Preview" class="w-full max-w-md mx-auto rounded-lg border border-gray-600">
                                                <div class="mt-2 text-center">
                                                    <button type="button" onclick="removePreviewImageDesktop()" class="text-red-500 hover:text-red-400 text-sm">
                                                        <i class="fas fa-trash mr-1"></i>Remove Image
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <p class="text-xs text-gray-400 mt-1" id="preview-content-help-desktop">
                                            Enter the URL or path for your course preview
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Thumbnail Section -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Thumbnail
                                </label>
                                <div class="space-y-4">
                                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md bg-gray-900">
                                        <div class="space-y-1 text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="flex text-sm text-gray-400">
                                                <label for="course-thumbnail-file" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-red-500 hover:text-red-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-red-500">
                                                    <span>Upload course thumbnail</span>
                                                    <input id="course-thumbnail-file" name="course_image" type="file" class="sr-only" accept="image/*">
                                                </label>
                                                <p class="pl-1">or drag and drop</p>
                                            </div>
                                            <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Thumbnail preview -->
                                    <div id="course-thumbnail-display" class="{{ $course->image ? '' : 'hidden' }}">
                                        <img id="course-thumbnail-preview" src="{{ $course->image ? $course->getImageUrl() : '' }}" alt="Course Thumbnail" class="w-full max-w-md mx-auto rounded-lg border border-gray-600">
                                        <div class="mt-2 text-center">
                                            <button type="button" onclick="removeCourseThumbnail()" class="text-red-500 hover:text-red-400 text-sm">
                                                <i class="fas fa-trash mr-1"></i>Remove Thumbnail
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <p class="text-xs text-gray-400">This image will be displayed on course listings and as the main course image. Recommended size: 400x225 pixels.</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <select name="category_id" id="category-select-desktop"
                                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 pr-12">
                                            <option value="">Select Category</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}" {{ $course->category_id == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <button type="button" id="add-category-btn-desktop"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-red-500 hover:text-red-400 transition-colors p-1 rounded"
                                                title="Add New Category">
                                            <i class="fas fa-plus text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Level <span class="text-red-500">*</span>
                                    </label>
                                    <select name="level"
                                            class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500">
                                        <option value="beginner" {{ $course->level == 'beginner' ? 'selected' : '' }}>Beginner</option>
                                        <option value="intermediate" {{ $course->level == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                        <option value="advanced" {{ $course->level == 'advanced' ? 'selected' : '' }}>Advanced</option>
                                        <option value="all_levels" {{ $course->level == 'all_levels' ? 'selected' : '' }}>All Levels</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Price (USD) <span class="text-red-500">*</span>
                                    </label>
                                    <input type="number" name="price" value="{{ $course->price }}" min="0" step="0.01"
                                           class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                           placeholder="0.00">
                                    <p class="text-xs text-gray-400 mt-1">Set to 0 for free course</p>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                                <button type="button" onclick="saveCourse()"
                                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                                    <i class="fas fa-save mr-2"></i>Save Course Details
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Chapter Editor -->
                <div id="chapter-editor" class="hidden">
                    <!-- Chapter editor content will be loaded dynamically -->
                </div>

                <!-- Lecture Editor -->
                <div id="lecture-editor" class="hidden">
                    <!-- Lecture editor content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="{{ csrf_token() }}">

@push('scripts')
<script>
// Simple, production-ready featured toggle function
function handleFeaturedToggle(button) {
    // Prevent double-clicks
    if (button.disabled) {
        return;
    }

    const courseSlug = button.getAttribute('data-course-slug');
    const currentFeatured = button.getAttribute('data-current-featured') === 'true';
    const newFeatured = !currentFeatured;

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        alert('CSRF token not found. Please refresh the page.');
        return;
    }

    // Disable button and show loading state
    button.disabled = true;
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1 md:mr-2"></i><span class="hidden sm:inline">Processing...</span><span class="sm:hidden">...</span>';

    // Make AJAX request
    fetch(`/instructor/courses/${courseSlug}/toggle-featured`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ featured: newFeatured })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                throw new Error('Invalid JSON response from server');
            }
        });
    })
    .then(data => {
        if (data.success) {
            // Update button state
            button.setAttribute('data-current-featured', newFeatured.toString());

            if (newFeatured) {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-yellow-600 hover:bg-yellow-700 text-white';
                button.innerHTML = '<i class="fas fa-star mr-1 md:mr-2"></i><span class="hidden sm:inline">Featured</span><span class="sm:hidden">Featured</span>';
            } else {
                button.className = 'px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors bg-gray-600 hover:bg-gray-700 text-white';
                button.innerHTML = '<i class="fas fa-star mr-1 md:mr-2"></i><span class="hidden sm:inline">Feature</span><span class="sm:hidden">Feature</span>';
            }

            // Show success message if showSuccess function exists
            if (typeof showSuccess === 'function') {
                showSuccess(data.message || (newFeatured ? 'Course marked as featured' : 'Course removed from featured'));
            }
        } else {
            alert(data.message || 'Failed to update featured status');
            button.innerHTML = originalContent;
        }
    })
    .catch(error => {
        alert(`Error: ${error.message}`);
        button.innerHTML = originalContent;
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
    });
}
</script>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-utils.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-main.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-drag-drop.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-autosave.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-editors.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-file-upload.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-lecture-editor.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-categories.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-api.js') }}"></script>
<script src="{{ asset('js/instructor/course-builder/course-builder-crud.js') }}"></script>


@endpush
@endsection
