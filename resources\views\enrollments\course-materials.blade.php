@extends('layouts.app')

@section('title', 'Course Materials - ' . $course->title)

@section('content')
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gray-900 border-b border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('my-courses.view', $course) }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Course
                </a>
                <a href="{{ route('my-courses') }}"
                   class="inline-flex items-center px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    My Courses
                </a>
            </div>

            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-white mb-2">{{ $course->title }}</h1>
                    <p class="text-gray-300 mb-4">{{ $course->description }}</p>

                    @if($enrollment)
                        <div class="flex items-center space-x-4 text-sm">
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full font-medium">
                                Enrolled
                            </span>
                            <span class="text-gray-400">
                                Progress: {{ $enrollment->progress }}%
                            </span>
                            <span class="text-gray-400">
                                Last Activity: {{ $enrollment->last_activity_at?->diffForHumans() ?? 'Never' }}
                            </span>
                        </div>
                    @endif
                </div>

                <div class="flex-shrink-0">
                    <div class="text-right text-sm text-gray-400">
                        <div>{{ $materials->count() }} Lectures Available</div>
                        @if($course->instructor)
                            <div>Instructor: {{ $course->instructor->name }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Materials List -->
    <div class="container mx-auto px-4 py-8">
        <div class="bg-gray-900 border border-gray-800 rounded-lg">
            <div class="p-6 border-b border-gray-800">
                <h2 class="text-2xl font-bold text-white">Course Lectures</h2>
                <p class="text-gray-400 mt-1">Access your course lectures and learning content</p>
            </div>

            @if($materials->count() > 0)
                <div class="divide-y divide-gray-800">
                    @foreach($materials as $lecture)
                        <div class="p-6 hover:bg-gray-800 transition-colors">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-4">
                                        <!-- Lecture Type Icon -->
                                        <div class="flex-shrink-0">
                                            @switch($lecture->type)
                                                @case('text')
                                                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    </div>
                                                    @break
                                                @case('video')
                                                    <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                        </svg>
                                                    </div>
                                                    @break
                                                @case('quiz')
                                                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                    </div>
                                                    @break
                                                @case('assignment')
                                                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                                        </svg>
                                                    </div>
                                                    @break
                                                @case('resource')
                                                    <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    </div>
                                                    @break
                                                @default
                                                    <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                        </svg>
                                                    </div>
                                            @endswitch
                                        </div>

                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <h3 class="text-xl font-semibold text-white">{{ $lecture->title }}</h3>
                                                @if($lecture->chapter)
                                                    <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">{{ $lecture->chapter->title }}</span>
                                                @endif
                                            </div>
                                            @if($lecture->description)
                                                <p class="text-gray-300 mb-2">{{ Str::limit($lecture->description, 150) }}</p>
                                            @endif

                                            <div class="flex items-center space-x-4 text-sm">
                                                <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded capitalize">{{ $lecture->type }}</span>
                                                @if($lecture->duration_minutes)
                                                    <span class="text-gray-400">{{ $lecture->duration_minutes }} min</span>
                                                @endif
                                                @if($lecture->sort_order)
                                                    <span class="text-gray-400">Lesson {{ $lecture->sort_order }}</span>
                                                @endif
                                                @if($lecture->created_at)
                                                    <span class="text-gray-400">Added {{ $lecture->created_at->diffForHumans() }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-3 ml-4">
                                    <a href="{{ route('my-courses.lecture', [$course, $lecture->chapter, $lecture]) }}"
                                       class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
                                        </svg>
                                        Start Lecture
                                    </a>

                                    @if($lecture->type === 'resource' && $lecture->resource_files)
                                        <a href="{{ route('files.lecture-resource', [$course, $lecture, 'download']) }}"
                                           class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            Download
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="p-12 text-center">
                    <div class="text-6xl mb-6">📚</div>
                    <h3 class="text-2xl font-bold text-white mb-4">No Materials Available</h3>
                    <p class="text-gray-400 mb-8 max-w-md mx-auto">This course doesn't have any published materials yet. Check back later or contact your instructor for more information.</p>
                    <a href="{{ route('my-courses.view', $course) }}"
                       class="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Course
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
