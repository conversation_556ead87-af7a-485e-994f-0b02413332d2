/**
 * Course Viewer - Professional LMS Interface JavaScript
 * Handles navigation, progress tracking, sidebar controls, and AJAX operations
 */

class CourseViewer {
    constructor() {
        this.courseId = null;
        this.currentLectureId = null;
        this.currentLectureSlug = null;
        this.currentChapterSlug = null;
        this.csrfToken = null;
        this.sidebarOpen = false;
        this.sidebarHiddenOnDesktop = false;
        this.isMobile = window.innerWidth < 1024;
        this.sidebarScrollPosition = 0;
        this.lectureStates = new Map(); // Track lecture completion states

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupResponsiveHandling();
        this.initializeSidebar();
        this.setupKeyboardShortcuts();

        // Handle browser back/forward navigation
        this.setupPopstateHandler();
        this.expandCurrentChapter();

        // Initialize lecture states
        this.initializeLectureStates();
    }

    setupEventListeners() {
        // Sidebar toggle (main content header)
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Sidebar hamburger toggle (sidebar header)
        const sidebarHamburgerToggle = document.querySelector('.sidebar-hamburger-toggle');
        if (sidebarHamburgerToggle) {
            sidebarHamburgerToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Sidebar close button
        const sidebarClose = document.querySelector('.sidebar-close-btn');
        if (sidebarClose) {
            sidebarClose.addEventListener('click', () => this.closeSidebar());
        }

        // Sidebar overlay
        const sidebarOverlay = document.querySelector('.sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => this.closeSidebar());
        }

        // Chapter toggles
        document.querySelectorAll('.chapter-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleChapter(e));
        });

        // Lecture navigation
        document.querySelectorAll('.lecture-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleLectureClick(e));
        });

        // Complete button
        const completeBtn = document.querySelector('.complete-btn');
        if (completeBtn) {
            completeBtn.addEventListener('click', (e) => this.toggleLectureComplete(e));
        }

        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleNavigation(e));
        });
    }

    setupResponsiveHandling() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth < 1024;

            // If switching from mobile to desktop, close mobile sidebar and reset desktop state
            if (wasMobile && !this.isMobile) {
                this.closeSidebar();
                this.showSidebarOnDesktop(); // Ensure sidebar is visible on desktop by default
            }

            // If switching from desktop to mobile, reset desktop sidebar state
            if (!wasMobile && this.isMobile) {
                this.showSidebarOnDesktop(); // Reset desktop classes
                this.closeSidebar(); // Close mobile sidebar by default
            }
        });
    }

    initializeSidebar() {
        // Get course and lecture IDs from meta tags or data attributes
        const courseIdMeta = document.querySelector('meta[name="course-id"]');
        const lectureIdMeta = document.querySelector('meta[name="current-lecture-id"]');
        const lectureSlugMeta = document.querySelector('meta[name="current-lecture-slug"]');
        const chapterSlugMeta = document.querySelector('meta[name="current-chapter-slug"]');
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        
        if (courseIdMeta) this.courseId = courseIdMeta.getAttribute('content');
        if (lectureIdMeta) this.currentLectureId = lectureIdMeta.getAttribute('content');
        if (lectureSlugMeta) this.currentLectureSlug = lectureSlugMeta.getAttribute('content');
        if (chapterSlugMeta) this.currentChapterSlug = chapterSlugMeta.getAttribute('content');
        if (csrfMeta) this.csrfToken = csrfMeta.getAttribute('content');

        // Initialize sidebar state for mobile
        if (this.isMobile) {
            this.closeSidebar();
        }
    }

    toggleSidebar() {
        if (this.isMobile) {
            // Mobile behavior - toggle open/close
            if (this.sidebarOpen) {
                this.closeSidebar();
            } else {
                this.openSidebar();
            }
        } else {
            // Desktop behavior - toggle hide/show
            if (this.sidebarHiddenOnDesktop) {
                this.showSidebarOnDesktop();
            } else {
                this.hideSidebarOnDesktop();
            }
        }
    }

    openSidebar() {
        if (!this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.add('active');
            sidebar.setAttribute('aria-hidden', 'false');
            this.sidebarOpen = true;
        }

        if (overlay) {
            overlay.classList.add('active');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Close sidebar');
        });

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    closeSidebar() {
        if (!this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.remove('active');
            sidebar.setAttribute('aria-hidden', 'true');
            this.sidebarOpen = false;
        }

        if (overlay) {
            overlay.classList.remove('active');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Open sidebar');
        });

        // Restore body scroll
        document.body.style.overflow = '';
    }

    hideSidebarOnDesktop() {
        if (this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const container = document.querySelector('.course-viewer-container');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.add('desktop-hidden');
            sidebar.setAttribute('aria-hidden', 'true');
            this.sidebarHiddenOnDesktop = true;
        }

        if (container) {
            container.classList.add('sidebar-hidden');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Show sidebar');
        });
    }

    showSidebarOnDesktop() {
        if (this.isMobile) return;

        const sidebar = document.querySelector('.course-sidebar');
        const container = document.querySelector('.course-viewer-container');
        const toggleButtons = document.querySelectorAll('.sidebar-toggle, .sidebar-hamburger-toggle');

        if (sidebar) {
            sidebar.classList.remove('desktop-hidden');
            sidebar.setAttribute('aria-hidden', 'false');
            this.sidebarHiddenOnDesktop = false;
        }

        if (container) {
            container.classList.remove('sidebar-hidden');
        }

        // Update button aria labels
        toggleButtons.forEach(btn => {
            btn.setAttribute('aria-label', 'Hide sidebar');
        });
    }

    toggleChapter(event) {
        event.preventDefault();
        const header = event.currentTarget;
        const chapter = header.closest('.chapter');
        const lecturesList = chapter.querySelector('.lectures-list');

        if (chapter.classList.contains('expanded')) {
            // Collapse chapter
            chapter.classList.remove('expanded');
            lecturesList.style.maxHeight = '0';
            lecturesList.style.opacity = '0';
        } else {
            // Expand chapter
            chapter.classList.add('expanded');
            lecturesList.style.maxHeight = lecturesList.scrollHeight + 'px';
            lecturesList.style.opacity = '1';
        }
    }

    expandCurrentChapter() {
        // Find and expand the chapter containing the current lecture
        const currentLecture = document.querySelector('.lecture-item.active');
        if (currentLecture) {
            const chapter = currentLecture.closest('.chapter');
            if (chapter && !chapter.classList.contains('expanded')) {
                const header = chapter.querySelector('.chapter-header');
                if (header) {
                    this.toggleChapter({ currentTarget: header, preventDefault: () => {} });
                }
            }
        }
    }

    handleLectureClick(event) {
        console.log('🎯 CourseViewer: Lecture click detected', event.currentTarget);
        event.preventDefault(); // Prevent default navigation
        event.stopPropagation(); // Prevent event bubbling

        const lectureItem = event.currentTarget;
        const lectureUrl = lectureItem.href;
        const lectureId = lectureItem.dataset.lectureId;

        console.log('🎯 CourseViewer: Processing lecture click', {
            url: lectureUrl,
            lectureId: lectureId,
            element: lectureItem
        });

        if (!lectureUrl) {
            console.error('❌ CourseViewer: No lecture URL found');
            return;
        }

        // Store current scroll position
        const sidebar = document.querySelector('.course-sidebar');
        this.sidebarScrollPosition = sidebar ? sidebar.scrollTop : 0;
        console.log('📍 CourseViewer: Stored scroll position:', this.sidebarScrollPosition);

        // Update visual states immediately for better UX
        this.updateLectureVisualStates(lectureId);

        // Add loading state
        lectureItem.classList.add('loading');

        // Load lecture content via AJAX
        console.log('🚀 CourseViewer: Starting AJAX navigation to:', lectureUrl);
        this.loadLectureContent(lectureUrl, this.sidebarScrollPosition)
            .then(() => {
                console.log('✅ CourseViewer: AJAX navigation successful');
                // Close sidebar after selection only on mobile
                if (this.isMobile) {
                    setTimeout(() => this.closeSidebar(), 300);
                }
            })
            .catch(error => {
                console.error('❌ CourseViewer: AJAX navigation failed, falling back to page reload:', error);
                // Fallback to normal navigation
                window.location.href = lectureUrl;
            })
            .finally(() => {
                lectureItem.classList.remove('loading');
            });
    }



    async toggleLectureComplete(event) {
        event.preventDefault();

        if (!this.courseId || !this.currentLectureId || !this.csrfToken) {
            console.error('Missing required data:', {
                courseId: this.courseId,
                currentLectureId: this.currentLectureId,
                csrfToken: this.csrfToken ? 'present' : 'missing'
            });
            this.showNotification('Missing required data for completion toggle', 'error');
            return;
        }

        const completeBtn = event.currentTarget;
        const originalContent = completeBtn.innerHTML;
        const isCompleted = completeBtn.dataset.completed === 'true';
        const action = isCompleted ? 'uncomplete' : 'complete';
        
        // Show loading state
        completeBtn.innerHTML = `
            <svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>${isCompleted ? 'Uncompleting...' : 'Completing...'}</span>
        `;
        completeBtn.disabled = true;

        try {
            const url = `/my-courses/${this.courseId}/${this.currentChapterSlug}/lecture/${this.currentLectureSlug}/${action}`;
            console.log('Making request to:', url);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('HTTP error response:', {
                    status: response.status,
                    statusText: response.statusText,
                    body: errorText
                });
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Server response:', data);

            if (data.success) {
                this.updateProgressUI(data);
                this.updateLectureStatus(isCompleted);
                const message = isCompleted ? 'Lecture marked as uncomplete! 📝' : 'Lecture completed! 🎉';
                this.showNotification(message, 'success');
            } else {
                throw new Error(data.message || `Failed to ${action} lecture`);
            }

        } catch (error) {
            console.error(`Error ${action}ing lecture:`, error);
            this.showNotification(`An error occurred while ${action}ing the lecture`, 'error');
            
            // Restore button
            completeBtn.innerHTML = originalContent;
            completeBtn.disabled = false;
        }
    }

    updateProgressUI(data) {
        console.log('Updating progress UI with data:', data);

        // Update progress bar
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = data.progress + '%';
            console.log('Updated progress bar to:', data.progress + '%');
        } else {
            console.error('Progress bar element not found');
        }

        // Update progress text
        const progressText = document.querySelector('.progress-percentage');
        if (progressText) {
            progressText.textContent = data.progress.toFixed(1) + '%';
            console.log('Updated progress text to:', data.progress.toFixed(1) + '%');
        } else {
            console.error('Progress percentage element not found');
        }

        // Update lecture count
        const lectureCount = document.querySelector('.lecture-count');
        if (lectureCount) {
            lectureCount.textContent = `${data.completed_lectures}/${data.total_lectures} lessons`;
            console.log('Updated lecture count to:', `${data.completed_lectures}/${data.total_lectures} lessons`);
        } else {
            console.error('Lecture count element not found');
        }
    }

    updateLectureStatus(wasCompleted = false) {
        console.log('Updating lecture status for lecture ID:', this.currentLectureId, 'wasCompleted:', wasCompleted);

        // Update current lecture in sidebar
        const currentLectureItem = document.querySelector(`.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        if (currentLectureItem) {
            if (wasCompleted) {
                // Was completed, now uncompleting
                currentLectureItem.classList.remove('completed');
                currentLectureItem.classList.add('active');
                this.lectureStates.set(this.currentLectureId, 'active');
            } else {
                // Was uncompleted, now completing
                currentLectureItem.classList.remove('active');
                currentLectureItem.classList.add('completed');
                this.lectureStates.set(this.currentLectureId, 'completed');
            }
            console.log('Updated lecture item classes');

            // Update status icon with improved visual indicators
            this.updateLectureStatusIcon(currentLectureItem, wasCompleted ? 'active' : 'completed');
        } else {
            console.error('Current lecture item not found with selector:', `.lecture-item[data-lecture-id="${this.currentLectureId}"]`);
        }

        // Update complete button
        const completeBtn = document.querySelector('.complete-btn');
        if (completeBtn) {
            if (wasCompleted) {
                // Was completed, now show mark as complete button
                completeBtn.innerHTML = `
                    <i class="fas fa-check"></i>
                    <span>Mark as Complete</span>
                `;
                completeBtn.classList.remove('completed');
                completeBtn.dataset.completed = 'false';
                completeBtn.disabled = false;
            } else {
                // Was uncompleted, now show mark as uncomplete button
                completeBtn.innerHTML = `
                    <i class="fas fa-undo"></i>
                    <span>Mark as Uncomplete</span>
                `;
                completeBtn.classList.add('completed');
                completeBtn.dataset.completed = 'true';
                completeBtn.disabled = false;
            }
            console.log('Updated complete button state');
        } else {
            console.error('Complete button not found');
        }
    }

    updateLectureVisualStates(newActiveLectureId) {
        // Remove active state from all lectures
        document.querySelectorAll('.lecture-item.active').forEach(item => {
            item.classList.remove('active');
            const lectureId = item.dataset.lectureId;

            // If this lecture was previously completed, restore completed state
            if (this.lectureStates.get(lectureId) === 'completed') {
                item.classList.add('completed');
                this.updateLectureStatusIcon(item, 'completed');
            } else {
                // Otherwise, set as pending
                this.updateLectureStatusIcon(item, 'pending');
            }
        });

        // Set new active lecture
        const newActiveLecture = document.querySelector(`.lecture-item[data-lecture-id="${newActiveLectureId}"]`);
        if (newActiveLecture) {
            newActiveLecture.classList.remove('completed');
            newActiveLecture.classList.add('active');
            this.updateLectureStatusIcon(newActiveLecture, 'active');
        }
    }

    updateLectureStatusIcon(lectureItem, state) {
        const statusIcon = lectureItem.querySelector('.lecture-status');
        if (!statusIcon) return;

        // Get lecture number for pending state
        const lectureNumber = Array.from(lectureItem.parentElement.children).indexOf(lectureItem) + 1;

        switch (state) {
            case 'active':
                statusIcon.className = 'lecture-status current';
                statusIcon.innerHTML = `<i class="fas fa-play"></i>`;
                break;
            case 'completed':
                statusIcon.className = 'lecture-status completed';
                statusIcon.innerHTML = `<i class="fas fa-check"></i>`;
                break;
            case 'pending':
            default:
                statusIcon.className = 'lecture-status pending';
                statusIcon.innerHTML = lectureNumber;
                break;
        }
    }

    storeLectureStates() {
        // Store current completion states
        document.querySelectorAll('.lecture-item').forEach(item => {
            const lectureId = item.dataset.lectureId;
            if (item.classList.contains('completed')) {
                this.lectureStates.set(lectureId, 'completed');
            } else if (item.classList.contains('active')) {
                this.lectureStates.set(lectureId, 'active');
            } else {
                this.lectureStates.set(lectureId, 'pending');
            }
        });
    }

    restoreLectureStates() {
        // Restore completion states after sidebar update
        document.querySelectorAll('.lecture-item').forEach(item => {
            const lectureId = item.dataset.lectureId;
            const storedState = this.lectureStates.get(lectureId);

            if (storedState) {
                // Remove all state classes
                item.classList.remove('active', 'completed');

                // Apply stored state
                if (storedState === 'completed') {
                    item.classList.add('completed');
                } else if (storedState === 'active') {
                    item.classList.add('active');
                }

                // Update visual indicator
                this.updateLectureStatusIcon(item, storedState);
            }
        });
    }



    async loadLectureContent(url, scrollPosition = 0) {
        try {
            console.log('🚀 CourseViewer: Loading lecture content from:', url);
            console.log('📍 CourseViewer: Target scroll position:', scrollPosition);

            // Add AJAX header to request
            const response = await fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'text/html',
                    'Cache-Control': 'no-cache'
                }
            });

            console.log('📡 CourseViewer: Response received:', response.status, response.statusText);
            console.log('📡 CourseViewer: Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }

            const html = await response.text();
            console.log('📄 CourseViewer: HTML response length:', html.length);

            // Check if we got a valid HTML response
            if (!html || html.length < 100) {
                throw new Error('Invalid or empty HTML response');
            }

            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            console.log('🔍 CourseViewer: Parsed document title:', doc.title);

            // Verify we got a valid document
            if (!doc || !doc.documentElement) {
                throw new Error('Failed to parse HTML response');
            }

            // Update the main content area
            const newContent = doc.querySelector('.course-content');
            const currentContent = document.querySelector('.course-content');

            console.log('🔄 CourseViewer: Content elements found:', {
                newContent: !!newContent,
                currentContent: !!currentContent
            });

            if (newContent && currentContent) {
                console.log('✅ CourseViewer: Updating main content');
                currentContent.innerHTML = newContent.innerHTML;
            } else {
                console.error('❌ CourseViewer: Content elements not found');
            }

            // Update sidebar active states while preserving completion states
            const newSidebar = doc.querySelector('.course-sidebar .curriculum-section');
            const currentSidebar = document.querySelector('.course-sidebar .curriculum-section');

            console.log('🔄 CourseViewer: Sidebar elements found:', {
                newSidebar: !!newSidebar,
                currentSidebar: !!currentSidebar
            });

            if (newSidebar && currentSidebar) {
                console.log('💾 CourseViewer: Storing lecture states');
                // Store current completion states before updating
                this.storeLectureStates();

                console.log('🔄 CourseViewer: Updating sidebar content');
                currentSidebar.innerHTML = newSidebar.innerHTML;

                console.log('🔄 CourseViewer: Restoring lecture states');
                // Restore completion states and apply proper visual indicators
                this.restoreLectureStates();

                // Restore scroll position with smooth scrolling
                const sidebar = document.querySelector('.course-sidebar');
                if (sidebar) {
                    console.log('📍 CourseViewer: Restoring scroll position to:', scrollPosition);
                    // Use smooth scrolling for better UX
                    sidebar.scrollTo({
                        top: scrollPosition,
                        behavior: 'smooth'
                    });
                }

                console.log('🔗 CourseViewer: Re-binding sidebar events');
                // Re-bind event listeners for new sidebar content
                this.bindSidebarEvents();
            } else {
                console.error('❌ CourseViewer: Sidebar elements not found');
            }

            // Update page title and meta tags
            const newTitle = doc.querySelector('title');
            if (newTitle) {
                document.title = newTitle.textContent;
            }

            // Update meta tags
            const metaTags = ['course-id', 'current-lecture-id', 'current-lecture-slug', 'current-chapter-slug'];
            metaTags.forEach(name => {
                const newMeta = doc.querySelector(`meta[name="${name}"]`);
                const currentMeta = document.querySelector(`meta[name="${name}"]`);
                if (newMeta && currentMeta) {
                    currentMeta.setAttribute('content', newMeta.getAttribute('content'));
                }
            });

            // Update browser URL without reload
            window.history.pushState({ scrollPosition }, '', url);

            // Re-initialize course viewer with new data
            this.initializeSidebar();
            this.expandCurrentChapter();

            // Re-bind complete button event
            const completeBtn = document.querySelector('.complete-btn');
            if (completeBtn) {
                completeBtn.addEventListener('click', (e) => this.toggleLectureComplete(e));
            }

            // Re-bind navigation buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.handleNavigation(e));
            });

        } catch (error) {
            console.error('❌ CourseViewer: Error loading lecture content:', error);
            console.error('❌ CourseViewer: Failed URL:', url);
            throw error;
        }
    }
    
    handleNavigation(event) {
        event.preventDefault();

        const navBtn = event.currentTarget;
        const navUrl = navBtn.href;

        if (!navUrl) return;

        // Store current scroll position
        const sidebar = document.querySelector('.course-sidebar');
        this.sidebarScrollPosition = sidebar ? sidebar.scrollTop : 0;

        // Add loading state
        navBtn.style.opacity = '0.7';

        // Load lecture content via AJAX
        this.loadLectureContent(navUrl, this.sidebarScrollPosition)
            .catch(error => {
                console.error('Error loading lecture:', error);
                // Fallback to normal navigation
                window.location.href = navUrl;
            })
            .finally(() => {
                navBtn.style.opacity = '';
            });
    }
    
    bindSidebarEvents() {
        // Re-bind chapter toggles
        document.querySelectorAll('.chapter-header').forEach(header => {
            header.addEventListener('click', (e) => this.toggleChapter(e));
        });
        
        // Re-bind lecture navigation
        document.querySelectorAll('.lecture-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleLectureClick(e));
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when not in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    const completeBtn = document.querySelector('.complete-btn:not(.completed)');
                    if (completeBtn) {
                        completeBtn.click();
                    }
                    break;
                    
                case 'ArrowLeft':
                    e.preventDefault();
                    const prevBtn = document.querySelector('.nav-btn[href*="lecture/"]:first-of-type');
                    if (prevBtn) {
                        this.handleNavigation({ currentTarget: prevBtn, preventDefault: () => {} });
                    }
                    break;
                    
                case 'ArrowRight':
                    e.preventDefault();
                    const nextBtn = document.querySelector('.nav-btn[href*="lecture/"]:last-of-type');
                    if (nextBtn) {
                        this.handleNavigation({ currentTarget: nextBtn, preventDefault: () => {} });
                    }
                    break;
                    
                case 'KeyS':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.toggleSidebar();
                    }
                    break;

                case 'KeyB':
                    // Toggle sidebar with 'B' key (for sidebar/bar)
                    e.preventDefault();
                    this.toggleSidebar();
                    break;
            }
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 9999;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        `;

        // Set background color based on type
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);

        // Close button
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }
    }

    setupPopstateHandler() {
        window.addEventListener('popstate', (event) => {
            // Handle browser back/forward navigation
            const scrollPosition = event.state?.scrollPosition || 0;

            // Reload the page content via AJAX to maintain sidebar state
            this.loadLectureContent(window.location.href, scrollPosition)
                .catch(() => {
                    // Fallback to page reload if AJAX fails
                    window.location.reload();
                });
        });
    }

    initializeLectureStates() {
        // Initialize lecture states from current DOM
        this.storeLectureStates();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎓 CourseViewer: Initializing...');
    new CourseViewer();

    // Add console filter helper
    console.log('💡 CourseViewer: To filter console logs, use: CourseViewer');
    console.log('💡 CourseViewer: To see only course viewer logs, filter by "CourseViewer:"');
});

// Export for potential external use
window.CourseViewer = CourseViewer;
