/* Course Viewer - Professional LMS Interface */
/* Dark theme with red accents, mobile-first responsive design */

/* ===== RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
}

.course-viewer {
    font-family: 'Inter', sans-serif;
    background-color: #000000;
    color: #ffffff;
    min-height: 100vh;
    line-height: 1.6;
}

/* ===== LAYOUT STRUCTURE ===== */
.course-viewer-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* ===== SIDEBAR STYLES ===== */
.course-sidebar {
    width: 100%;
    max-width: 400px;
    background-color: #111827;
    border-right: 1px solid #1f2937;
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 40;
    scroll-behavior: smooth;
}

/* Mobile sidebar - hidden by default */
@media (max-width: 1023px) {
    .course-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }
    
    .course-sidebar.active {
        transform: translateX(0);
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 35;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

/* Desktop sidebar */
@media (min-width: 1024px) {
    .course-sidebar {
        position: sticky;
        top: 0;
        height: 100vh;
        transition: transform 0.3s ease, width 0.3s ease;
    }

    /* Desktop sidebar hidden state */
    .course-sidebar.desktop-hidden {
        transform: translateX(-100%);
        width: 0;
        min-width: 0;
        overflow: hidden;
    }

    /* Adjust main content when sidebar is hidden on desktop */
    .course-viewer-container.sidebar-hidden .course-content {
        margin-left: 0;
    }
}

/* ===== SIDEBAR HEADER ===== */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #1f2937;
    position: sticky;
    top: 0;
    background-color: #111827;
    z-index: 10;
}

.sidebar-close-btn {
    display: none;
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.sidebar-close-btn:hover {
    color: #ef4444;
    background-color: #1f2937;
}

@media (max-width: 1023px) {
    .sidebar-close-btn {
        display: block;
    }
}

.course-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
}

.course-progress {
    margin-top: 1rem;
}

.progress-bar-container {
    background-color: #1f2937;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ef4444, #dc2626);
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #9ca3af;
}

.progress-percentage {
    font-weight: 600;
    color: #ef4444;
}

/* ===== CURRICULUM SECTION ===== */
.curriculum-section {
    padding: 1.5rem;
}

.curriculum-title {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.curriculum-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #ef4444;
}

/* Sidebar hamburger toggle button */
.sidebar-hamburger-toggle {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-hamburger-toggle:hover {
    background-color: #1f2937;
}

.sidebar-hamburger-toggle:hover .curriculum-icon {
    color: #ffffff;
}

/* ===== CHAPTER STYLES ===== */
.chapter {
    margin-bottom: 1.5rem;
}

.chapter-header {
    padding: 1rem;
    background-color: #1f2937;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.chapter-header:hover {
    background-color: #374151;
    border-color: #ef4444;
}

.chapter-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chapter-toggle {
    color: #9ca3af;
    transition: transform 0.2s ease;
}

.chapter.expanded .chapter-toggle {
    transform: rotate(180deg);
}

.chapter-description {
    font-size: 0.8rem;
    color: #9ca3af;
    margin: 0.5rem 0 0 0;
    line-height: 1.4;
}

/* ===== LECTURE STYLES ===== */
.lectures-list {
    margin-left: 0.5rem;
    border-left: 2px solid #1f2937;
    padding-left: 0;
}

.lecture-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #d1d5db;
    transition: all 0.3s ease;
    border-radius: 0.375rem;
    margin: 0.25rem 0;
    position: relative;
    border-left: 3px solid transparent;
}

.lecture-item:hover {
    background-color: #1f2937;
    color: #ffffff;
    transform: translateX(2px);
    border-left-color: #4b5563;
}

/* Loading state for lecture items */
.lecture-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lecture-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 1rem;
    width: 1rem;
    height: 1rem;
    border: 2px solid #374151;
    border-top: 2px solid #ef4444;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

.lecture-item.active {
    background-color: #ef4444;
    color: #ffffff;
    font-weight: 600;
    border-left: 3px solid #ef4444;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.lecture-item.completed {
    background-color: #065f46;
    border-left: 3px solid #10b981;
    color: #d1fae5;
}

.lecture-item.completed:hover {
    background-color: #047857;
    color: #ffffff;
}

.lecture-status {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.lecture-status.pending {
    background-color: #374151;
    color: #9ca3af;
    border: 1px solid #4b5563;
}

.lecture-status.current {
    background-color: #ef4444;
    color: #ffffff;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
    animation: pulse-red 2s infinite;
}

.lecture-status.completed {
    background-color: #10b981;
    color: #ffffff;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

/* Pulse animation for current lecture */
@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
    }
}

.lecture-content {
    flex: 1;
    min-width: 0;
}

.lecture-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
    word-wrap: break-word;
}

.lecture-meta {
    font-size: 0.75rem;
    color: #9ca3af;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lecture-duration {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== MAIN CONTENT AREA ===== */
.course-content {
    flex: 1;
    background-color: #000000;
    overflow-y: auto;
}

/* Mobile content adjustment */
@media (max-width: 1023px) {
    .course-content {
        width: 100%;
    }
}

/* ===== CONTENT HEADER ===== */
.content-header {
    background-color: #111827;
    border-bottom: 1px solid #1f2937;
    padding: 1rem 1.5rem;
    position: sticky;
    top: 0;
    z-index: 30;
}

.header-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.sidebar-toggle {
    display: block;
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    color: #ef4444;
    background-color: #1f2937;
}

.lecture-navigation {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #1f2937;
    color: #d1d5db;
    text-decoration: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #374151;
}

.nav-btn:hover {
    background-color: #374151;
    color: #ffffff;
    border-color: #ef4444;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn.primary {
    background-color: #ef4444;
    color: #ffffff;
    border-color: #ef4444;
}

.nav-btn.primary:hover {
    background-color: #dc2626;
}

/* ===== LECTURE CONTENT ===== */
.lecture-content-area {
    padding: 2rem 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .lecture-content-area {
        padding: 1.5rem 1rem;
    }
}

.lecture-header {
    margin-bottom: 2rem;
}

.lecture-title-main {
    font-size: 1.875rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 1rem 0;
    line-height: 1.3;
}

@media (max-width: 768px) {
    .lecture-title-main {
        font-size: 1.5rem;
    }
}

.lecture-meta-main {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #9ca3af;
    font-size: 0.875rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== CONTENT TYPES ===== */
.content-wrapper {
    background-color: #111827;
    border-radius: 0.75rem;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #1f2937;
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: 1.5rem;
    }
}

/* Video Content */
.video-container {
    position: relative;
    width: 100%;
    margin-bottom: 1.5rem;
}

.video-player {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    background-color: #000000;
}

.video-embed {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 0.5rem;
}

.video-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0.5rem;
}

/* Text Content */
.text-content {
    font-size: 1rem;
    line-height: 1.7;
    color: #e5e7eb;
}

.text-content h1, .text-content h2, .text-content h3, 
.text-content h4, .text-content h5, .text-content h6 {
    color: #ffffff;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
}

.text-content p {
    margin-bottom: 1rem;
}

.text-content ul, .text-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.text-content li {
    margin-bottom: 0.5rem;
}

.text-content a {
    color: #ef4444;
    text-decoration: underline;
}

.text-content a:hover {
    color: #dc2626;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #1f2937;
}

.navigation-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .navigation-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1rem;
    }
}

.complete-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #ef4444;
    color: #ffffff;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.complete-btn:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
}

.complete-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.complete-btn.completed {
    background-color: #10b981;
    color: #ffffff;
}

.complete-btn.completed:hover {
    background-color: #059669;
}

/* ===== EMPTY STATE ===== */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #9ca3af;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 640px) {
    .course-sidebar {
        max-width: 100%;
    }
    
    .sidebar-header {
        padding: 1rem;
    }
    
    .curriculum-section {
        padding: 1rem;
    }
    
    .content-header {
        padding: 1rem;
    }
    
    .lecture-content-area {
        padding: 1rem;
    }
    
    .content-wrapper {
        padding: 1rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* ===== SCROLLBAR STYLING ===== */
.course-sidebar::-webkit-scrollbar,
.course-content::-webkit-scrollbar {
    width: 6px;
}

.course-sidebar::-webkit-scrollbar-track,
.course-content::-webkit-scrollbar-track {
    background: #1f2937;
}

.course-sidebar::-webkit-scrollbar-thumb,
.course-content::-webkit-scrollbar-thumb {
    background: #ef4444;
    border-radius: 3px;
}

.course-sidebar::-webkit-scrollbar-thumb:hover,
.course-content::-webkit-scrollbar-thumb:hover {
    background: #dc2626;
}
