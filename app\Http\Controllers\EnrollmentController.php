<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Lecture;
use App\Models\LearningMaterial;
use App\Models\Ebook;
use App\Models\Resource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EnrollmentController extends Controller
{
    public function enroll(Course $course)
    {
        $user = auth()->user();

        // Check if already enrolled
        if ($user->enrollments()->where('course_id', $course->id)->exists()) {
            return back()->with('error', 'You are already enrolled in this course.');
        }

        // Check if course requires payment
        if ($course->price > 0) {
            return redirect()->route('courses.show', $course)
                ->with('error', 'This course requires payment. Please use the payment option to enroll.');
        }

        // Create enrollment for free courses only
        Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'instructor_id' => $course->instructor_id,
            'enrolled_at' => now(),
            'status' => 'active',
            'progress_percentage' => 0,
            'completed_lectures' => 0,
            'total_lectures' => $course->lectures()->count(),
            'last_accessed_at' => now()
        ]);

        return redirect()->route('my-courses')->with('success', 'Successfully enrolled in ' . $course->title . '!');
    }
    
    public function myCourses()
    {
        $user = auth()->user();

        // Get enrollments with detailed course information
        $enrollments = $user->enrollments()
            ->with([
                'course.instructor',
                'course.chapters.lectures',
                'course.category',
                'course.subcategory'
            ])
            ->orderBy('last_accessed_at', 'desc')
            ->get();

        // Calculate overall progress statistics
        $stats = [
            'total_courses' => $enrollments->count(),
            'completed_courses' => $enrollments->where('status', 'completed')->count(),
            'in_progress_courses' => $enrollments->where('status', 'active')->where('progress_percentage', '>', 0)->count(),
            'not_started_courses' => $enrollments->where('progress_percentage', 0)->count(),
            'total_watch_time' => $enrollments->sum('total_watch_time_minutes'),
            'average_progress' => $enrollments->avg('progress_percentage') ?? 0,
            'certificates_earned' => $enrollments->where('certificate_issued', true)->count(),
        ];

        // Get recently accessed courses
        $recentCourses = $enrollments->where('last_accessed_at', '!=', null)
            ->sortByDesc('last_accessed_at')
            ->take(3);

        // Get recommended courses based on enrolled categories
        $enrolledCategories = $enrollments->pluck('course.category')->unique()->filter();
        $recommendedCourses = collect();

        if ($enrolledCategories->isNotEmpty()) {
            $recommendedCourses = Course::where('status', 'published')
                ->whereIn('category', $enrolledCategories)
                ->whereNotIn('id', $enrollments->pluck('course_id'))
                ->with('instructor')
                ->inRandomOrder()
                ->take(4)
                ->get();
        }

        return view('enrollments.my-courses', compact('enrollments', 'stats', 'recentCourses', 'recommendedCourses'));
    }
    
    public function viewCourse(Course $course)
    {
        $user = auth()->user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->firstOrFail();

        // Load course with complete structure
        $course->load([
            'instructor',
            'chapters' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lectures' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            }
        ]);

        // Get current lecture (last accessed or first lecture)
        $currentLecture = null;
        if ($enrollment->current_lecture_id) {
            $currentLecture = Lecture::find($enrollment->current_lecture_id);
        }

        // If no current lecture, get the first available lecture
        if (!$currentLecture && $course->chapters->isNotEmpty()) {
            $firstChapter = $course->chapters->first();
            if ($firstChapter && $firstChapter->lectures->isNotEmpty()) {
                $currentLecture = $firstChapter->lectures->first();
            }
        }

        // Get user's lecture progress
        $completedLectureIds = $enrollment->completed_lecture_ids ?? [];

        // Calculate detailed progress
        $totalLectures = $course->chapters->sum(function($chapter) {
            return $chapter->lectures->count();
        });

        $completedLectures = count($completedLectureIds);
        $progressPercentage = $totalLectures > 0 ? round(($completedLectures / $totalLectures) * 100, 1) : 0;

        // Update enrollment progress if different
        if ($enrollment->progress_percentage != $progressPercentage) {
            $enrollment->update([
                'progress_percentage' => $progressPercentage,
                'completed_lectures' => $completedLectures,
                'total_lectures' => $totalLectures
            ]);
        }

        return view('enrollments.view-course', compact('course', 'enrollment', 'currentLecture', 'completedLectureIds'));
    }

    /**
     * View a specific lecture within a course
     */
    public function viewLecture(Course $course, \App\Models\Chapter $chapter, Lecture $lecture)
    {
        $user = auth()->user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->firstOrFail();

        // Verify lecture belongs to this course and chapter
        if ($lecture->course_id !== $course->id || $lecture->chapter_id !== $chapter->id) {
            abort(404);
        }

        // Load course structure
        $course->load([
            'instructor',
            'chapters' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lectures' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            }
        ]);

        // Update last accessed lecture
        $enrollment->update([
            'current_lecture_id' => $lecture->id,
            'last_accessed_at' => now()
        ]);

        // Get user's lecture progress
        $completedLectureIds = $enrollment->completed_lecture_ids ?? [];

        return view('enrollments.view-course', compact('course', 'enrollment', 'lecture', 'completedLectureIds'))
            ->with('currentLecture', $lecture);
    }

    /**
     * Mark a lecture as completed
     */
    public function completeLecture(Course $course, \App\Models\Chapter $chapter, Lecture $lecture)
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
            }

            $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

            if (!$enrollment) {
                return response()->json(['success' => false, 'message' => 'You are not enrolled in this course'], 403);
            }

            // Verify lecture belongs to this course and chapter
            if ($lecture->course_id !== $course->id || $lecture->chapter_id !== $chapter->id) {
                return response()->json(['success' => false, 'message' => 'Invalid lecture for this course'], 400);
            }

            $completedLectureIds = $enrollment->completed_lecture_ids ?? [];

            if (!in_array($lecture->id, $completedLectureIds)) {
                $completedLectureIds[] = $lecture->id;

                // Calculate new progress
                $totalLectures = $course->chapters()->where('is_published', true)
                    ->withCount(['lectures' => function($query) {
                        $query->where('is_published', true);
                    }])
                    ->get()
                    ->sum('lectures_count');

                $completedCount = count($completedLectureIds);
                $progressPercentage = $totalLectures > 0 ? round(($completedCount / $totalLectures) * 100, 1) : 0;

                // Update enrollment
                $enrollment->update([
                    'completed_lecture_ids' => $completedLectureIds,
                    'completed_lectures' => $completedCount,
                    'progress_percentage' => $progressPercentage,
                    'total_watch_time_minutes' => $enrollment->total_watch_time_minutes + ($lecture->duration_minutes ?? 0),
                    'last_accessed_at' => now()
                ]);

                // Check if course is completed
                if ($progressPercentage >= 100) {
                    $enrollment->update([
                        'status' => 'completed',
                        'completed_at' => now()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'progress' => $enrollment->fresh()->progress_percentage,
                'completed_lectures' => count($completedLectureIds),
                'total_lectures' => $totalLectures ?? 0
            ]);

        } catch (\Exception $e) {
            \Log::error('Error marking lecture as complete', [
                'user_id' => auth()->id(),
                'course_id' => $course->id,
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while marking the lecture as complete. Please try again.'
            ], 500);
        }
    }

    /**
     * Mark a lecture as uncompleted
     */
    public function uncompleteLecture(Course $course, \App\Models\Chapter $chapter, Lecture $lecture)
    {
        try {
            $user = auth()->user();
            
            // Verify the lecture belongs to the course and chapter
            if ($lecture->chapter_id !== $chapter->id || $chapter->course_id !== $course->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid lecture or chapter.'
                ], 400);
            }

            // Get user's enrollment
            $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
            
            if (!$enrollment) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not enrolled in this course.'
                ], 403);
            }

            // Get current completed lecture IDs
            $completedLectureIds = $enrollment->completed_lecture_ids ?? [];
            
            // Remove lecture ID if it exists in completed list
            if (in_array($lecture->id, $completedLectureIds)) {
                $completedLectureIds = array_values(array_filter($completedLectureIds, function($id) use ($lecture) {
                    return $id !== $lecture->id;
                }));
                
                // Get total lectures count for progress calculation
                $totalLectures = $course->publishedLectures()->count();
                
                // Calculate new progress
                $completedCount = count($completedLectureIds);
                $progressPercentage = $totalLectures > 0 ? round(($completedCount / $totalLectures) * 100, 1) : 0;
                
                // Update enrollment
                $enrollment->update([
                    'completed_lecture_ids' => $completedLectureIds,
                    'completed_lectures' => $completedCount,
                    'progress_percentage' => $progressPercentage,
                    'status' => $progressPercentage < 100 ? 'active' : 'completed'
                ]);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Lecture marked as uncompleted.',
                    'progress' => $progressPercentage,
                    'completed_lectures' => $completedCount,
                    'total_lectures' => $totalLectures
                ]);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Lecture is not marked as completed.'
            ], 400);
            
        } catch (\Exception $e) {
            \Log::error('Error marking lecture as uncomplete', [
                'user_id' => auth()->id(),
                'course_id' => $course->id,
                'lecture_id' => $lecture->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while marking the lecture as uncomplete. Please try again.'
            ], 500);
        }
    }

    /**
     * Display course materials for enrolled users
     */
    public function courseMaterials(Course $course)
    {
        $user = auth()->user();

        // Get published lectures for the course through chapters
        $materials = $course->publishedLectures()
            ->orderBy('chapters.sort_order')
            ->orderBy('lectures.sort_order')
            ->get();

        // Get user's enrollment for progress tracking
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

        return view('enrollments.course-materials', compact('course', 'materials', 'enrollment'));
    }

    /**
     * View a specific course material
     */
    public function viewMaterial(Course $course, LearningMaterial $material)
    {
        // Ensure material belongs to the course
        if ($material->course_id !== $course->id) {
            abort(404, 'Material not found in this course.');
        }

        // Check if material is published
        if (!$material->is_published) {
            abort(404, 'Material not available.');
        }

        $user = auth()->user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

        return view('enrollments.view-material', compact('course', 'material', 'enrollment'));
    }

    /**
     * Download a course material file
     */
    public function downloadMaterial(Course $course, LearningMaterial $material)
    {
        // Ensure material belongs to the course
        if ($material->course_id !== $course->id) {
            abort(404, 'Material not found in this course.');
        }

        // Check if material is published and has a file
        if (!$material->is_published || !$material->hasFile()) {
            abort(404, 'File not available.');
        }

        // Check if file exists in storage
        if (!Storage::disk('private')->exists($material->file_path)) {
            abort(404, 'File not found.');
        }

        // Return file download response
        return Storage::disk('private')->download(
            $material->file_path,
            $material->file_name ?? basename($material->file_path)
        );
    }

    /**
     * View a specific course ebook
     */
    public function viewEbook(Course $course, Ebook $ebook)
    {
        // Ensure ebook belongs to the course
        if ($ebook->course_id !== $course->id) {
            abort(404, 'Ebook not found in this course.');
        }

        // Check if ebook is published
        if (!$ebook->is_published) {
            abort(404, 'Ebook not available.');
        }

        $user = auth()->user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

        return view('enrollments.view-ebook', compact('course', 'ebook', 'enrollment'));
    }

    /**
     * Download a course ebook file
     */
    public function downloadEbook(Course $course, Ebook $ebook)
    {
        // Ensure ebook belongs to the course
        if ($ebook->course_id !== $course->id) {
            abort(404, 'Ebook not found in this course.');
        }

        // Check if ebook is published and downloadable
        if (!$ebook->is_published || !$ebook->is_downloadable || !$ebook->file_path) {
            abort(404, 'File not available for download.');
        }

        // Check if file exists in storage
        if (!Storage::disk('private')->exists($ebook->file_path)) {
            abort(404, 'File not found.');
        }

        // Return file download response
        return Storage::disk('private')->download(
            $ebook->file_path,
            $ebook->file_name ?? basename($ebook->file_path)
        );
    }

    /**
     * View a specific course resource
     */
    public function viewResource(Course $course, Resource $resource)
    {
        // Ensure resource belongs to the course
        if ($resource->course_id !== $course->id) {
            abort(404, 'Resource not found in this course.');
        }

        // Check if resource is published
        if (!$resource->is_published) {
            abort(404, 'Resource not available.');
        }

        $user = auth()->user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

        return view('enrollments.view-resource', compact('course', 'resource', 'enrollment'));
    }

    /**
     * Download a course resource file
     */
    public function downloadResource(Course $course, Resource $resource)
    {
        // Ensure resource belongs to the course
        if ($resource->course_id !== $course->id) {
            abort(404, 'Resource not found in this course.');
        }

        // Check if resource is published and has a file
        if (!$resource->is_published || !$resource->file_path) {
            abort(404, 'File not available.');
        }

        // Check if file exists in storage
        if (!Storage::disk('private')->exists($resource->file_path)) {
            abort(404, 'File not found.');
        }

        // Return file download response
        return Storage::disk('private')->download(
            $resource->file_path,
            $resource->file_name ?? basename($resource->file_path)
        );
    }
}
