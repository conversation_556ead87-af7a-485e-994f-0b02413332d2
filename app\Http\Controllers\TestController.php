<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use App\Models\Enrollment;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TestController extends Controller
{
    /**
     * Test RBAC permissions and system integrity
     */
    public function systemTest()
    {
        $results = [
            'timestamp' => now()->toDateTimeString(),
            'tests' => []
        ];

        // Test 1: Database Seeder Data Integrity
        $results['tests']['database_seeder'] = $this->testDatabaseSeeder();

        // Test 2: Course Catalog Functionality
        $results['tests']['course_catalog'] = $this->testCourseCatalog();

        // Test 3: PayPal Integration
        $results['tests']['paypal_integration'] = $this->testPayPalIntegration();

        // Test 4: My Courses Functionality
        $results['tests']['my_courses'] = $this->testMyCourses();

        // Test 5: Course Content Viewing
        $results['tests']['course_viewing'] = $this->testCourseViewing();

        // Test 6: RBAC Permissions
        $results['tests']['rbac_permissions'] = $this->testRBACPermissions();

        // Test 7: Performance Checks
        $results['tests']['performance'] = $this->testPerformance();

        return view('test.system-test', compact('results'));
    }

    /**
     * Test the complete student journey
     */
    public function studentJourney()
    {
        $course = Course::with('publishedChapters.publishedLectures')->first();
        $student = User::where('email', '<EMAIL>')->first();
        $enrollment = $student->enrollments()->where('course_id', $course->id)->first();

        return response()->json([
            'success' => true,
            'course' => [
                'id' => $course->id,
                'title' => $course->title,
                'slug' => $course->slug,
                'chapters' => $course->publishedChapters->count(),
                'lectures' => $course->lectures()->count(),
                'instructor' => $course->instructor->name,
            ],
            'student' => [
                'id' => $student->id,
                'name' => $student->name,
                'email' => $student->email,
            ],
            'enrollment' => $enrollment ? [
                'id' => $enrollment->id,
                'status' => $enrollment->status,
                'progress' => $enrollment->progress_percentage,
                'completed_lectures' => count($enrollment->completed_lecture_ids ?? []),
                'total_lectures' => $enrollment->total_lectures,
                'enrolled_at' => $enrollment->enrolled_at,
            ] : null,
            'test_urls' => [
                'course_detail' => route('courses.show', $course),
                'my_courses' => route('my-courses'),
                'view_course' => $enrollment ? route('my-courses.view', $course) : null,
                'first_lecture' => $enrollment && $course->lectures()->first() ?
                    route('my-courses.lecture', [$course, $course->lectures()->first()->chapter, $course->lectures()->first()]) : null,
            ],
            'hierarchy_test' => [
                'chapters' => $course->publishedChapters->map(function($chapter) {
                    return [
                        'id' => $chapter->id,
                        'title' => $chapter->title,
                        'lectures_count' => $chapter->publishedLectures->count(),
                        'lectures' => $chapter->publishedLectures->map(function($lecture) {
                            return [
                                'id' => $lecture->id,
                                'title' => $lecture->title,
                                'type' => $lecture->type,
                                'duration' => $lecture->duration_minutes,
                                'is_free_preview' => $lecture->is_free_preview,
                            ];
                        })
                    ];
                })
            ]
        ]);
    }

    private function testDatabaseSeeder()
    {
        $test = [
            'name' => 'Database Seeder Data Integrity',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Check if courses exist
            $courseCount = Course::count();
            $test['details']['total_courses'] = $courseCount;

            if ($courseCount === 0) {
                $test['status'] = 'fail';
                $test['issues'][] = 'No courses found in database';
            }

            // Check course structure
            $coursesWithChapters = Course::has('chapters')->count();
            $test['details']['courses_with_chapters'] = $coursesWithChapters;

            $coursesWithLectures = Course::whereHas('chapters.lectures')->count();
            $test['details']['courses_with_lectures'] = $coursesWithLectures;

            // Check instructors
            $instructorCount = User::whereHas('roles', function($q) {
                $q->where('name', 'instructor');
            })->count();
            $test['details']['instructor_count'] = $instructorCount;

            if ($instructorCount === 0) {
                $test['status'] = 'fail';
                $test['issues'][] = 'No instructors found';
            }

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'Database error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testCourseCatalog()
    {
        $test = [
            'name' => 'Course Catalog Functionality',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Test published courses
            $publishedCourses = Course::where('status', 'published')->count();
            $test['details']['published_courses'] = $publishedCourses;

            // Test categories
            $categories = Course::distinct()->pluck('category')->filter()->count();
            $test['details']['unique_categories'] = $categories;

            // Test course relationships
            $coursesWithInstructors = Course::has('instructor')->count();
            $test['details']['courses_with_instructors'] = $coursesWithInstructors;

            if ($publishedCourses === 0) {
                $test['status'] = 'warning';
                $test['issues'][] = 'No published courses available';
            }

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'Course catalog error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testPayPalIntegration()
    {
        $test = [
            'name' => 'PayPal Integration',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Check PayPal configuration
            $paypalClientId = config('services.paypal.client_id');
            $paypalSecret = config('services.paypal.client_secret');
            $paypalMode = config('services.paypal.mode');

            $test['details']['paypal_mode'] = $paypalMode;
            $test['details']['client_id_configured'] = !empty($paypalClientId);
            $test['details']['secret_configured'] = !empty($paypalSecret);

            if (empty($paypalClientId) || empty($paypalSecret)) {
                $test['status'] = 'fail';
                $test['issues'][] = 'PayPal credentials not configured';
            }

            // Check payment records
            $paymentCount = Payment::count();
            $completedPayments = Payment::where('status', Payment::STATUS_COMPLETED)->count();
            
            $test['details']['total_payments'] = $paymentCount;
            $test['details']['completed_payments'] = $completedPayments;

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'PayPal integration error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testMyCourses()
    {
        $test = [
            'name' => 'My Courses Functionality',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Check enrollments
            $enrollmentCount = Enrollment::count();
            $activeEnrollments = Enrollment::where('status', 'active')->count();
            $completedEnrollments = Enrollment::where('status', 'completed')->count();

            $test['details']['total_enrollments'] = $enrollmentCount;
            $test['details']['active_enrollments'] = $activeEnrollments;
            $test['details']['completed_enrollments'] = $completedEnrollments;

            // Check enrollment relationships
            $enrollmentsWithCourses = Enrollment::has('course')->count();
            $enrollmentsWithUsers = Enrollment::has('user')->count();

            $test['details']['enrollments_with_courses'] = $enrollmentsWithCourses;
            $test['details']['enrollments_with_users'] = $enrollmentsWithUsers;

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'My Courses error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testCourseViewing()
    {
        $test = [
            'name' => 'Course Content Viewing',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Check course structure
            $chaptersCount = DB::table('chapters')->count();
            $lecturesCount = DB::table('lectures')->count();
            $publishedChapters = DB::table('chapters')->where('is_published', true)->count();
            $publishedLectures = DB::table('lectures')->where('is_published', true)->count();

            $test['details']['total_chapters'] = $chaptersCount;
            $test['details']['total_lectures'] = $lecturesCount;
            $test['details']['published_chapters'] = $publishedChapters;
            $test['details']['published_lectures'] = $publishedLectures;

            if ($lecturesCount === 0) {
                $test['status'] = 'warning';
                $test['issues'][] = 'No lectures found for course content';
            }

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'Course viewing error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testRBACPermissions()
    {
        $test = [
            'name' => 'RBAC Permissions',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Check roles
            $roles = DB::table('roles')->pluck('name')->toArray();
            $test['details']['available_roles'] = $roles;

            $requiredRoles = ['student', 'instructor', 'admin', 'superadmin'];
            $missingRoles = array_diff($requiredRoles, $roles);

            if (!empty($missingRoles)) {
                $test['status'] = 'fail';
                $test['issues'][] = 'Missing required roles: ' . implode(', ', $missingRoles);
            }

            // Check user role assignments
            $usersWithRoles = DB::table('user_roles')->count();
            $test['details']['users_with_roles'] = $usersWithRoles;

            // Check permissions
            $permissions = DB::table('permissions')->count();
            $test['details']['total_permissions'] = $permissions;

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'RBAC error: ' . $e->getMessage();
        }

        return $test;
    }

    private function testPerformance()
    {
        $test = [
            'name' => 'Performance Checks',
            'status' => 'pass',
            'details' => [],
            'issues' => []
        ];

        try {
            // Database query performance
            $start = microtime(true);
            Course::with(['instructor', 'chapters.lectures'])->take(10)->get();
            $queryTime = (microtime(true) - $start) * 1000;

            $test['details']['course_query_time_ms'] = round($queryTime, 2);

            if ($queryTime > 1000) { // More than 1 second
                $test['status'] = 'warning';
                $test['issues'][] = 'Slow database queries detected';
            }

            // Memory usage
            $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB
            $test['details']['memory_usage_mb'] = round($memoryUsage, 2);

            if ($memoryUsage > 128) { // More than 128MB
                $test['status'] = 'warning';
                $test['issues'][] = 'High memory usage detected';
            }

        } catch (\Exception $e) {
            $test['status'] = 'fail';
            $test['issues'][] = 'Performance test error: ' . $e->getMessage();
        }

        return $test;
    }
}
