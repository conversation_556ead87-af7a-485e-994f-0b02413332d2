<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Viewer Fixes Test</title>
    <link rel="stylesheet" href="public/css/course-viewer.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="course-id" content="1">
    <meta name="current-lecture-id" content="3">
    <meta name="current-lecture-slug" content="lecture-3">
    <meta name="current-chapter-slug" content="chapter-1">
    <meta name="csrf-token" content="test-token">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #0f172a;
            color: #e2e8f0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #1e293b;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #334155;
            z-index: 1000;
            max-width: 300px;
        }
        .test-info h3 {
            margin: 0 0 0.5rem 0;
            color: #ef4444;
        }
        .test-info ul {
            margin: 0;
            padding-left: 1.2rem;
            font-size: 0.875rem;
        }
        .test-info li {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h3>🧪 Test Features</h3>
        <ul>
            <li>✅ Sidebar scroll position memory</li>
            <li>✅ AJAX navigation (no page reload)</li>
            <li>✅ Visual state indicators</li>
            <li>✅ Smooth transitions</li>
            <li>✅ Loading states</li>
        </ul>
    </div>

    <div class="course-viewer-container">
        <!-- Course Sidebar -->
        <aside class="course-sidebar" role="complementary" aria-label="Course Navigation">
            <div class="sidebar-content">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <button class="sidebar-close-btn" aria-label="Close sidebar">
                        <i class="fas fa-times"></i>
                    </button>

                    <h1 class="course-title">Advanced JavaScript Course</h1>
                    
                    <!-- Course Progress -->
                    <div class="course-progress">
                        <div class="progress-info">
                            <span class="progress-text lecture-count">3 of 12 lessons</span>
                            <span class="progress-percentage">25.0%</span>
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 25%"></div>
                        </div>
                    </div>
                </div>

                <!-- Course Curriculum -->
                <div class="curriculum">
                    <!-- Chapter 1 -->
                    <div class="chapter expanded">
                        <div class="chapter-header">
                            <div class="chapter-info">
                                <h3 class="chapter-title">Chapter 1: JavaScript Fundamentals</h3>
                                <span class="chapter-meta">5 lectures • 45min</span>
                            </div>
                            <div class="chapter-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="lectures-list" style="max-height: 500px; opacity: 1;">
                            <a href="#lecture-1" class="lecture-item completed" data-lecture-id="1" data-lecture-title="Introduction to JavaScript">
                                <div class="lecture-status completed">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Introduction to JavaScript</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 8min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>

                            <a href="#lecture-2" class="lecture-item completed" data-lecture-id="2" data-lecture-title="Variables and Data Types">
                                <div class="lecture-status completed">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Variables and Data Types</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 12min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>

                            <a href="#lecture-3" class="lecture-item active" data-lecture-id="3" data-lecture-title="Functions and Scope">
                                <div class="lecture-status current">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Functions and Scope</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 15min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>

                            <a href="#lecture-4" class="lecture-item" data-lecture-id="4" data-lecture-title="Objects and Arrays">
                                <div class="lecture-status pending">4</div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Objects and Arrays</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 18min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>

                            <a href="#lecture-5" class="lecture-item" data-lecture-id="5" data-lecture-title="Control Flow">
                                <div class="lecture-status pending">5</div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Control Flow</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 10min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Chapter 2 -->
                    <div class="chapter">
                        <div class="chapter-header">
                            <div class="chapter-info">
                                <h3 class="chapter-title">Chapter 2: Advanced Concepts</h3>
                                <span class="chapter-meta">7 lectures • 65min</span>
                            </div>
                            <div class="chapter-toggle">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>

                        <div class="lectures-list" style="max-height: 0; opacity: 0;">
                            <a href="#lecture-6" class="lecture-item" data-lecture-id="6" data-lecture-title="Closures">
                                <div class="lecture-status pending">1</div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Closures</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 12min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>

                            <a href="#lecture-7" class="lecture-item" data-lecture-id="7" data-lecture-title="Promises and Async/Await">
                                <div class="lecture-status pending">2</div>
                                <div class="lecture-content">
                                    <h4 class="lecture-title">Promises and Async/Await</h4>
                                    <div class="lecture-meta">
                                        <span class="lecture-duration"><i class="fas fa-clock"></i> 20min</span>
                                        <span class="lecture-type"><i class="fas fa-play-circle"></i> Video</span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="course-content">
            <div class="content-header">
                <button class="sidebar-toggle" aria-label="Toggle sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <h2>Functions and Scope</h2>
            </div>

            <div class="lecture-content">
                <div class="video-container">
                    <div style="background: #1e293b; padding: 4rem; text-align: center; border-radius: 8px;">
                        <i class="fas fa-play-circle" style="font-size: 4rem; color: #ef4444; margin-bottom: 1rem;"></i>
                        <h3>Video Player Placeholder</h3>
                        <p>This would contain the actual lecture video content</p>
                    </div>
                </div>

                <div class="lecture-actions">
                    <button class="complete-btn" data-lecture-id="3" data-completed="false">
                        <i class="fas fa-check"></i>
                        <span>Mark as Complete</span>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="public/js/course-viewer.js"></script>
    <script>
        // Mock AJAX responses for testing
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            // Mock lecture content responses
            if (url.includes('#lecture-')) {
                const lectureId = url.match(/#lecture-(\d+)/)[1];
                const mockResponse = `
                    <div class="content-header">
                        <button class="sidebar-toggle" aria-label="Toggle sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h2>Lecture ${lectureId} Content</h2>
                    </div>
                    <div class="lecture-content">
                        <div class="video-container">
                            <div style="background: #1e293b; padding: 4rem; text-align: center; border-radius: 8px;">
                                <i class="fas fa-play-circle" style="font-size: 4rem; color: #ef4444; margin-bottom: 1rem;"></i>
                                <h3>Lecture ${lectureId} Video</h3>
                                <p>This is the content for lecture ${lectureId}</p>
                            </div>
                        </div>
                        <div class="lecture-actions">
                            <button class="complete-btn" data-lecture-id="${lectureId}" data-completed="false">
                                <i class="fas fa-check"></i>
                                <span>Mark as Complete</span>
                            </button>
                        </div>
                    </div>
                `;
                
                return Promise.resolve({
                    ok: true,
                    text: () => Promise.resolve(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Lecture ${lectureId}</title>
                            <meta name="current-lecture-id" content="${lectureId}">
                        </head>
                        <body>
                            <div class="course-content">${mockResponse}</div>
                            <div class="course-sidebar">
                                <div class="sidebar-content">
                                    ${document.querySelector('.sidebar-content').innerHTML}
                                </div>
                            </div>
                        </body>
                        </html>
                    `)
                });
            }
            
            // Fall back to original fetch for other requests
            return originalFetch.apply(this, arguments);
        };

        // Add some test content to scroll
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Course Viewer Test Page Loaded');
            console.log('✅ Click on different lectures to test AJAX navigation');
            console.log('✅ Scroll the sidebar and navigate to test position memory');
            console.log('✅ Check visual indicators for active/completed/pending states');
        });
    </script>
</body>
</html>
