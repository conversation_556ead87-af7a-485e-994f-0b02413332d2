// Debug script to test course viewer functionality
// Add this to your browser console to test AJAX navigation

console.log('🧪 Course Viewer Debug Script');

// Test 1: Check if Course<PERSON>iewer is loaded
if (window.CourseViewer) {
    console.log('✅ CourseViewer class is available');
} else {
    console.log('❌ CourseViewer class not found');
}

// Test 2: Check for course viewer instance
const courseViewerElements = document.querySelectorAll('.course-sidebar');
console.log(`📊 Found ${courseViewerElements.length} course sidebar(s)`);

// Test 3: Check lecture items
const lectureItems = document.querySelectorAll('.lecture-item');
console.log(`📚 Found ${lectureItems.length} lecture item(s)`);

// Test 4: Monitor fetch requests (only your domain)
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && !url.includes('play.google.com')) {
        console.log('🌐 CourseViewer Fetch:', url);
    }
    return originalFetch.apply(this, args);
};

// Test 5: Check for AJAX navigation
lectureItems.forEach((item, index) => {
    if (index < 3) { // Only log first 3 to avoid spam
        console.log(`🔗 Lecture ${index + 1}:`, item.href, item.dataset.lectureId);
    }
});

// Test 6: Simulate click (uncomment to test)
// if (lectureItems.length > 1) {
//     console.log('🖱️ Simulating click on second lecture...');
//     lectureItems[1].click();
// }

console.log('🎯 Debug script loaded. Click on lectures to test AJAX navigation.');
console.log('💡 Filter console by "CourseViewer" to see only relevant logs.');
