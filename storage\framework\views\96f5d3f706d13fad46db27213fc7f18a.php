<!-- Lecture Header -->
<header class="content-header">
    <div class="lecture-meta">
        <div class="lecture-type">
            <?php switch($lecture->type):
                case ('video'): ?>
                    <i class="fas fa-play-circle"></i>
                    <span>Video</span>
                    <?php break; ?>
                <?php case ('text'): ?>
                    <i class="fas fa-file-text"></i>
                    <span>Reading</span>
                    <?php break; ?>
                <?php case ('quiz'): ?>
                    <i class="fas fa-question-circle"></i>
                    <span>Quiz</span>
                    <?php break; ?>
                <?php case ('assignment'): ?>
                    <i class="fas fa-tasks"></i>
                    <span>Assignment</span>
                    <?php break; ?>
                <?php case ('resource'): ?>
                    <i class="fas fa-download"></i>
                    <span>Resource</span>
                    <?php break; ?>
                <?php default: ?>
                    <i class="fas fa-file"></i>
                    <span>Content</span>
            <?php endswitch; ?>
        </div>
        <?php if($lecture->duration): ?>
            <div class="lecture-duration">
                <i class="fas fa-clock"></i>
                <span><?php echo e($lecture->duration); ?> min</span>
            </div>
        <?php endif; ?>
    </div>
    <h1 class="lecture-title"><?php echo e($lecture->title); ?></h1>
    <?php if($lecture->description): ?>
        <p class="lecture-description"><?php echo e($lecture->description); ?></p>
    <?php endif; ?>
</header>

<!-- Lecture Content -->
<div class="content-wrapper">
    <?php switch($lecture->type):
        case ('video'): ?>
            <div class="video-container">
                <?php if($lecture->video_url): ?>
                    <?php if(str_contains($lecture->video_url, 'youtube.com') || str_contains($lecture->video_url, 'youtu.be')): ?>
        <?php
            // Convert YouTube watch URL to embed URL
            $embedUrl = $lecture->video_url;
            if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $lecture->video_url, $matches)) {
                $videoId = $matches[1];
                $embedUrl = "https://www.youtube.com/embed/{$videoId}";
            }
        ?>
        <div class="video-embed">
            <iframe src="<?php echo e($embedUrl); ?>"
                    title="<?php echo e($lecture->title); ?>"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowfullscreen>
            </iframe>
        </div>
                    <?php else: ?>
                        <video class="video-player" controls>
                            <source src="<?php echo e($lecture->video_url); ?>" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">🎥</div>
                        <h3>Video Coming Soon</h3>
                        <p>The video content for this lecture will be available soon.</p>
                    </div>
                <?php endif; ?>
            </div>
            <?php break; ?>

        <?php case ('text'): ?>
            <div class="text-content">
                <?php echo $lecture->content ?? '<p>Content coming soon...</p>'; ?>

            </div>
            <?php break; ?>

        <?php case ('quiz'): ?>
            <div class="quiz-content">
                <h3>Quiz: <?php echo e($lecture->title); ?></h3>
                <?php if($lecture->content): ?>
                    <?php echo $lecture->content; ?>

                <?php else: ?>
                    <p>Quiz content will be available soon.</p>
                <?php endif; ?>
            </div>
            <?php break; ?>

        <?php case ('assignment'): ?>
            <div class="assignment-content">
                <h3>Assignment: <?php echo e($lecture->title); ?></h3>
                <?php if($lecture->content): ?>
                    <?php echo $lecture->content; ?>

                <?php else: ?>
                    <p>Assignment details will be available soon.</p>
                <?php endif; ?>
            </div>
            <?php break; ?>

        <?php case ('resource'): ?>
            <div class="resource-content">
                <h3>Resource: <?php echo e($lecture->title); ?></h3>
                
                <?php if($lecture->content): ?>
                    <div class="resource-description">
                        <?php echo $lecture->content; ?>

                    </div>
                <?php endif; ?>
                
                <?php if($lecture->resources && is_array($lecture->resources) && count($lecture->resources) > 0 && isset($lecture->resources[0]['name'])): ?>
    <div class="resource-files mt-4">
        <h4 class="text-lg font-semibold mb-3">📁 Downloadable Files</h4>
        <div class="files-list space-y-2">
            <?php $__currentLoopData = $lecture->resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="file-item bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                                    <div class="file-info flex items-center">
                                        <div class="file-icon mr-3">
                                            <?php
                                                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                                                $iconClass = match(strtolower($extension)) {
                                                    'pdf' => 'fas fa-file-pdf text-red-500',
                                                    'doc', 'docx' => 'fas fa-file-word text-blue-500',
                                                    'xls', 'xlsx' => 'fas fa-file-excel text-green-500',
                                                    'ppt', 'pptx' => 'fas fa-file-powerpoint text-orange-500',
                                                    'zip', 'rar' => 'fas fa-file-archive text-purple-500',
                                                    'jpg', 'jpeg', 'png', 'gif' => 'fas fa-file-image text-pink-500',
                                                    default => 'fas fa-file text-gray-500'
                                                };
                                            ?>
                                            <i class="<?php echo e($iconClass); ?> text-2xl"></i>
                                        </div>
                                        <div class="file-details">
                                            <div class="file-name font-medium text-gray-900"><?php echo e($file['name']); ?></div>
                                            <div class="file-meta text-sm text-gray-500">
                                                <?php echo e(number_format(($file['file_size'] ?? $file['size'] ?? 0) / 1024, 1)); ?> KB
                                                <?php if(isset($file['uploaded_at'])): ?>
                                                    • Uploaded <?php echo e(\Carbon\Carbon::parse($file['uploaded_at'])->diffForHumans()); ?>

                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="file-actions">
                                        <?php
                                            // Extract filename from file_path or use direct filename
                                            $filename = isset($file['file_path']) ? basename($file['file_path']) : ($file['filename'] ?? '');
                                        ?>
                                        <?php if($filename): ?>
                                            <a href="<?php echo e(route('files.download-course-material', ['courseId' => $course->id, 'filename' => $filename])); ?>"
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center"
                                               download>
                                                <i class="fas fa-download mr-2"></i>
                                                Download
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
                <?php elseif($lecture->resources && is_array($lecture->resources) && isset($lecture->resources['url']) && $lecture->resources['url']): ?>
                    <div class="resource-url mt-4">
                        <h4 class="text-lg font-semibold mb-3">🔗 External Resource</h4>
                        <a href="<?php echo e($lecture->resources['url']); ?>" 
                           target="_blank" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Access Resource
                        </a>
                    </div>
                <?php elseif(!$lecture->content): ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">📄</div>
                        <h3>Resource Coming Soon</h3>
                        <p>Resource will be available for download soon.</p>
                    </div>
                <?php endif; ?>
            </div>
            <?php break; ?>

        <?php default: ?>
            <div class="default-content">
                <?php if($lecture->content): ?>
                    <?php echo $lecture->content; ?>

                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-state-icon">📄</div>
                        <h3>Content Coming Soon</h3>
                        <p>The content for this lecture will be available soon.</p>
                    </div>
                <?php endif; ?>
            </div>
    <?php endswitch; ?>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <div class="navigation-buttons">
            <?php
                // Get previous and next lectures
                $allLectures = collect();
                foreach($course->chapters as $chapter) {
                    foreach($chapter->lectures as $chapterLecture) {
                        $allLectures->push($chapterLecture);
                    }
                }
                $currentIndex = $allLectures->search(function($item) use ($lecture) {
                    return $item->id === $lecture->id;
                });
                $prevLecture = $currentIndex > 0 ? $allLectures[$currentIndex - 1] : null;
                $nextLecture = $currentIndex < $allLectures->count() - 1 ? $allLectures[$currentIndex + 1] : null;
            ?>
            
            <?php if($prevLecture): ?>
                <a href="<?php echo e(route('my-courses.lecture', [$course, $prevLecture->chapter, $prevLecture])); ?>" class="nav-btn ajax-lecture-link">
                    <i class="fas fa-chevron-left"></i>
                    <span>Previous Lesson</span>
                </a>
            <?php endif; ?>

            <?php if($nextLecture): ?>
                <a href="<?php echo e(route('my-courses.lecture', [$course, $nextLecture->chapter, $nextLecture])); ?>" class="nav-btn primary ajax-lecture-link">
                    <span>Next Lesson</span>
                    <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>

        <!-- Toggle Complete Button -->
        <?php if(!in_array($lecture->id, $completedLectureIds ?? [])): ?>
            <button class="complete-btn" data-lecture-id="<?php echo e($lecture->id); ?>" data-completed="false">
                <i class="fas fa-check"></i>
                <span>Mark as Complete</span>
            </button>
        <?php else: ?>
            <button class="complete-btn completed" data-lecture-id="<?php echo e($lecture->id); ?>" data-completed="true">
                <i class="fas fa-undo"></i>
                <span>Mark as Uncomplete</span>
            </button>
        <?php endif; ?>
    </div>
</div><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/enrollments/partials/lecture-content.blade.php ENDPATH**/ ?>